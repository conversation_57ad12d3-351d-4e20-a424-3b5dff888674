<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hina | All-in-One Discord Bot</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* Monochrome Theme */
            --black: #000000;
            --white: #ffffff;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
            
            --primary: var(--black);
            --primary-light: var(--gray-800);
            --primary-dark: var(--black);
            --accent: var(--gray-200);
            --text: var(--black);
            --text-light: var(--gray-600);
            --bg: var(--white);
            --bg-secondary: var(--gray-100);
            --card-bg: var(--white);
            --header-bg: rgba(255, 255, 255, 0.95);
            --footer-bg: var(--black);
            --shadow-color: rgba(0, 0, 0, 0.05);
            --loading-bg: var(--white);
            --gradient: linear-gradient(135deg, var(--black), var(--gray-800));
            
            /* Dark theme colors */
            --primary-dark-mode: var(--white);
            --primary-dark-dark: var(--gray-200);
            --primary-light-dark: var(--gray-400);
            --accent-dark: var(--gray-800);
            --white-dark: var(--black);
            --gray-light-dark: var(--gray-900);
            --gray-dark: var(--gray-800);
            --gray-dark-dark: var(--gray-700);
            --text-dark: var(--white);
            --text-light-dark: var(--gray-400);
            --bg-dark: var(--black);
            --bg-secondary-dark: var(--gray-900);
            --card-bg-dark: var(--gray-800);
            --header-bg-dark: rgba(0, 0, 0, 0.95);
            --footer-bg-dark: var(--gray-900);
            --shadow-color-dark: rgba(255, 255, 255, 0.05);
            --loading-bg-dark: var(--black);
            --gradient-dark: linear-gradient(135deg, var(--white), var(--gray-400));
            
            /* Animation variables */
            --transition-speed: 0.4s;
            --transition-easing: cubic-bezier(0.65, 0, 0.35, 1);
            --glass-blur: 8px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        body {
            background-color: var(--bg);
            color: var(--text);
            min-height: 100vh;
            overflow-x: hidden;
            transition: background-color var(--transition-speed) var(--transition-easing),
                        color var(--transition-speed) var(--transition-easing);
        }
        
        body.dark-theme {
            --primary: var(--primary-dark-mode);
            --primary-dark: var(--primary-dark-dark);
            --primary-light: var(--primary-light-dark);
            --accent: var(--accent-dark);
            --white: var(--white-dark);
            --gray-light: var(--gray-light-dark);
            --gray: var(--gray-dark);
            --gray-dark: var(--gray-dark-dark);
            --text: var(--text-dark);
            --text-light: var(--text-light-dark);
            --bg: var(--bg-dark);
            --bg-secondary: var(--bg-secondary-dark);
            --card-bg: var(--card-bg-dark);
            --header-bg: var(--header-bg-dark);
            --footer-bg: var(--footer-bg-dark);
            --shadow-color: var(--shadow-color-dark);
            --loading-bg: var(--loading-bg-dark);
            --gradient: var(--gradient-dark);
        }
        
        /* Theme toggle styles */
        .theme-toggle {
            position: relative;
            width: 50px;
            height: 26px;
            border-radius: 50px;
            background: var(--gray-300);
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-easing);
            border: none;
            outline: none;
            display: flex;
            align-items: center;
            padding: 0 5px;
            justify-content: space-between;
            box-shadow: 0 2px 4px var(--shadow-color);
        }
        
        .theme-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }
        
        .theme-toggle.dark {
            background: var(--gray-700);
        }
        
        .theme-toggle-icon {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text);
            transition: transform var(--transition-speed) var(--transition-easing),
                        color var(--transition-speed) var(--transition-easing);
        }
        
        .theme-toggle-icon.sun {
            transform: translateX(0) scale(1);
            opacity: 1;
        }
        
        .theme-toggle-icon.moon {
            transform: translateX(0) scale(0);
            opacity: 0;
        }
        
        .theme-toggle.dark .theme-toggle-icon.sun {
            transform: translateX(0) scale(0);
            opacity: 0;
        }
        
        .theme-toggle.dark .theme-toggle-icon.moon {
            transform: translateX(0) scale(1);
            opacity: 1;
        }
        
        .theme-toggle-thumb {
            position: absolute;
            left: 4px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--black);
            transition: all var(--transition-speed) var(--transition-easing);
            z-index: 1;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .theme-toggle.dark .theme-toggle-thumb {
            transform: translateX(24px);
            background: var(--white);
        }
        
        /* Loading Screen Styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--loading-bg);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out, background-color var(--transition-speed) var(--transition-easing);
        }
        
        .loading-container {
            text-align: center;
            position: relative;
            z-index: 10;
            max-width: 600px;
            padding: 2rem;
        }
        
        .loading-logo {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--black);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            transition: color var(--transition-speed) var(--transition-easing);
        }
        
        .loading-logo span {
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: var(--gray-800);
            border-radius: 50%;
            margin-left: 6px;
            transition: background-color var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .loading-logo {
            color: var(--white);
        }
        
        .dark-theme .loading-logo span {
            background-color: var(--gray-400);
        }
        
        .loading-text {
            font-size: 1.5rem;
            color: var(--text);
            margin-bottom: 3rem;
            font-weight: 500;
            transition: color var(--transition-speed) var(--transition-easing);
        }
        
        .progress-container {
            width: 100%;
            height: 8px;
            background-color: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 3rem;
            transition: background-color var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .progress-container {
            background-color: var(--gray-800);
        }
        
        .progress-bar {
            height: 100%;
            width: 0;
            background: var(--gradient);
            border-radius: 4px;
            transition: width 0.3s ease, background var(--transition-speed) var(--transition-easing);
            position: relative;
            overflow: hidden;
        }
        
        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                            transparent, 
                            rgba(255, 255, 255, 0.5), 
                            transparent);
            animation: shimmer 2s infinite;
            transition: background var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .progress-bar::after {
            background: linear-gradient(90deg, 
                            transparent, 
                            rgba(0, 0, 0, 0.5), 
                            transparent);
        }
        
        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }
        
        .loading-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 2rem;
            flex-wrap: wrap;
        }
        
        .stat-item {
            margin: 1rem;
            padding: 1rem;
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 6px var(--shadow-color);
            min-width: 120px;
            transition: all var(--transition-speed) var(--transition-easing);
            border: 1px solid var(--gray-200);
        }
        
        .dark-theme .stat-item {
            border-color: var(--gray-700);
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--black);
            margin-bottom: 0.5rem;
            transition: color var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .stat-value {
            color: var(--white);
        }
        
        .stat-label {
            color: var(--gray-600);
            font-size: 0.9rem;
            transition: color var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .stat-label {
            color: var(--gray-400);
        }
        
        .spinner {
            width: 70px;
            height: 70px;
            margin: 2rem auto;
            position: relative;
        }
        
        .spinner-dot {
            position: absolute;
            width: 16px;
            height: 16px;
            background: var(--black);
            border-radius: 50%;
            animation: spinner-animation 2s infinite ease-in-out;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .spinner-dot {
            background: var(--white);
        }
        
        .spinner-dot:nth-child(1) {
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 0s;
        }
        
        .spinner-dot:nth-child(2) {
            top: 15px;
            right: 15px;
            animation-delay: 0.2s;
        }
        
        .spinner-dot:nth-child(3) {
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            animation-delay: 0.4s;
        }
        
        .spinner-dot:nth-child(4) {
            bottom: 15px;
            right: 15px;
            animation-delay: 0.6s;
        }
        
        .spinner-dot:nth-child(5) {
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 0.8s;
        }
        
        .spinner-dot:nth-child(6) {
            bottom: 15px;
            left: 15px;
            animation-delay: 1s;
        }
        
        .spinner-dot:nth-child(7) {
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            animation-delay: 1.2s;
        }
        
        .spinner-dot:nth-child(8) {
            top: 15px;
            left: 15px;
            animation-delay: 1.4s;
        }
        
        @keyframes spinner-animation {
            0%, 20%, 80%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(0.5);
                opacity: 0.5;
            }
        }
        
        /* Main Content Styles (hidden initially) */
        .main-content {
            opacity: 0;
            transition: opacity 0.5s ease-in;
        }
        
        .gradient-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 10% 20%, var(--gray-200), transparent 30%),
                        radial-gradient(circle at 90% 80%, var(--gray-200), transparent 30%);
            z-index: -1;
            opacity: 0.1;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .gradient-bg {
            background: radial-gradient(circle at 10% 20%, var(--gray-800), transparent 30%),
                        radial-gradient(circle at 90% 80%, var(--gray-800), transparent 30%);
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            opacity: 0.05;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .shape-1 {
            width: 300px;
            height: 300px;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            background-color: var(--black);
            top: 10%;
            left: 5%;
            animation: float 8s ease-in-out infinite;
        }
        
        .dark-theme .shape-1 {
            background-color: var(--white);
        }
        
        .shape-2 {
            width: 200px;
            height: 200px;
            border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
            background-color: var(--gray-800);
            bottom: 20%;
            right: 10%;
            animation: float 10s ease-in-out infinite;
        }
        
        .dark-theme .shape-2 {
            background-color: var(--gray-400);
        }
        
        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(5deg);
            }
            100% {
                transform: translateY(0) rotate(0deg);
            }
        }
        
        .glow-effect {
            position: absolute;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, var(--gray-400), transparent 70%);
            filter: blur(60px);
            z-index: -1;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .glow-effect {
            background: radial-gradient(circle, var(--gray-600), transparent 70%);
        }
        
        .glow-1 {
            top: 20%;
            left: 10%;
        }
        
        .glow-2 {
            bottom: 15%;
            right: 10%;
        }
        
        /* Glass Morphism Header */
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 5%;
            background-color: var(--header-bg);
            box-shadow: 0 1px 3px var(--shadow-color);
            position: sticky;
            top: 0;
            z-index: 100;
            transition: all var(--transition-speed) var(--transition-easing);
            backdrop-filter: blur(var(--glass-blur));
            -webkit-backdrop-filter: blur(var(--glass-blur));
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--black);
            display: flex;
            align-items: center;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .logo {
            color: var(--white);
        }
        
        .logo span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: var(--gray-800);
            border-radius: 50%;
            margin-left: 4px;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .logo span {
            background-color: var(--gray-400);
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        nav a {
            text-decoration: none;
            color: var(--text);
            font-weight: 500;
            position: relative;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        nav a:hover {
            color: var(--black);
        }
        
        .dark-theme nav a:hover {
            color: var(--white);
        }
        
        nav a::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--black);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme nav a::after {
            background-color: var(--white);
        }
        
        nav a:hover::after {
            width: 100%;
        }
        
        /* Hero Section */
        .hero {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 8rem 2rem 6rem;
            position: relative;
        }
        
        .hero h1 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: var(--gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            line-height: 1.2;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .hero p {
            font-size: 1.2rem;
            max-width: 700px;
            margin-bottom: 3rem;
            color: var(--gray-600);
            line-height: 1.6;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .hero p {
            color: var(--gray-400);
        }
        
        .cta-button {
            background: var(--gradient);
            color: var(--white);
            border: none;
            padding: 0.8rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
               .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--gray-800), var(--black));
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }
        
        .dark-theme .cta-button::before {
            background: linear-gradient(135deg, var(--gray-400), var(--white));
        }
        
        .cta-button:hover::before {
            opacity: 1;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        
        .cta-button:active {
            transform: translateY(0);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        
        /* Features Section */
        .features {
            padding: 5rem 5%;
            background-color: var(--bg-secondary);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--black);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .section-header h2 {
            color: var(--white);
        }
        
        .section-header p {
            color: var(--gray-600);
            max-width: 600px;
            margin: 0 auto;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .section-header p {
            color: var(--gray-400);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .feature-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px var(--shadow-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }
        
        .dark-theme .feature-card {
            border-color: var(--gray-700);
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--gradient);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px var(--shadow-color);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background-color: var(--gray-200);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .feature-icon {
            background-color: var(--gray-700);
        }
        
        .feature-icon svg {
            width: 30px;
            height: 30px;
            color: var(--black);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .feature-icon svg {
            color: var(--white);
        }
        
        .feature-card h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--black);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .feature-card h3 {
            color: var(--white);
        }
        
        .feature-card p {
            color: var(--gray-600);
            line-height: 1.6;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .feature-card p {
            color: var(--gray-400);
        }
        
        /* Stats Section */
        .stats {
            padding: 5rem 5%;
            background-color: var(--bg);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            text-align: center;
        }
        
        .stats .stat-item {
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: all var(--transition-speed) var(--transition-easing);
            border: 1px solid var(--gray-200);
        }
        
        .dark-theme .stats .stat-item {
            border-color: var(--gray-700);
        }
        
        .stats .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px var(--shadow-color);
        }
        
        .stats .stat-item h3 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            transition: all var(--transition-speed) var(--transition-easing);
            background: var(--gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .stats .stat-item p {
            color: var(--gray-600);
            font-weight: 500;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .stats .stat-item p {
            color: var(--gray-400);
        }

/* Testimonials Section */
        .testimonials {
            padding: 5rem 5%;
            background-color: var(--bg-secondary);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .testimonial-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: all 0.3s ease;
            position: relative;
            border: 1px solid var(--gray-200);
        }
        
        .dark-theme .testimonial-card {
            border-color: var(--gray-700);
        }
        
        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px var(--shadow-color);
        }
        
        .testimonial-content {
            font-style: italic;
            color: var(--text);
            margin-bottom: 1.5rem;
            line-height: 1.6;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
        }
        
        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--gray-200);
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--black);
            font-weight: bold;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .author-avatar {
            background-color: var(--gray-700);
            color: var(--white);
        }
        
        .author-info h4 {
            color: var(--text);
            margin-bottom: 0.2rem;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .author-info p {
            color: var(--gray-600);
            font-size: 0.9rem;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .author-info p {
            color: var(--gray-400);
        }
        
        /* Newsletter Section */
        .newsletter {
            padding: 5rem 5%;
            background: var(--gradient);
            color: white;
            text-align: center;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .newsletter-container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .newsletter h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .newsletter p {
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .newsletter-form {
            display: flex;
            gap: 1rem;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .newsletter-input {
            flex: 1;
            padding: 0.8rem 1rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            outline: none;
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .dark-theme .newsletter-input {
            background-color: rgba(0, 0, 0, 0.3);
            color: white;
        }
        
        .newsletter-button {
            background-color: white;
            color: var(--black);
            border: none;
            padding: 0.8rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .dark-theme .newsletter-button {
            color: var(--black);
        }
        
        .newsletter-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* Footer */
        footer {
            background-color: var(--footer-bg);
            color: var(--white);
            padding: 4rem 5% 2rem;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }
        
        .footer-column h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            position: relative;
            color: var(--white);
        }
        
        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 2px;
            background-color: var(--gray-400);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .footer-column ul {
            list-style: none;
        }
        
        .footer-column li {
            margin-bottom: 0.8rem;
        }
        
        .footer-column a {
            color: var(--gray-400);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .footer-column a:hover {
            color: var(--white);
            padding-left: 5px;
        }
        
        .footer-column p {
            color: var(--gray-400);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--gray-400);
            transition: all 0.3s ease;
        }
        
        .social-links a:hover {
            background-color: var(--gray-600);
            color: white;
            transform: translateY(-3px);
        }
        
        .copyright {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--gray-400);
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        /* Animated background elements */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            pointer-events: none;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background-color: var(--gray-800);
            border-radius: 50%;
            opacity: 0.3;
            animation: float-particle 15s infinite linear;
            transition: all var(--transition-speed) var(--transition-easing);
        }
        
        .dark-theme .particle {
            background-color: var(--gray-400);
        }
        
        @keyframes float-particle {
            0% {
                transform: translateY(0) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100vh) translateX(100px);
                opacity: 0;
            }
        }


              /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--gradient);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            cursor: pointer;
            z-index: 90;
            transition: all 0.3s ease;
        }
        
        .fab:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        
        .fab i {
            font-size: 1.5rem;
        }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 3rem 1rem;
            }
            
            .hero h1 {
                font-size: 3.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .hero {
                padding: 6rem 2rem 4rem;
            }
            
            .hero h1 {
                font-size: 3rem;
            }
            
            nav ul {
                gap: 1.5rem;
            }
            
            .loading-logo {
                font-size: 2rem;
            }
            
            .loading-text {
                font-size: 1.2rem;
            }
            
            .stat-item {
                min-width: 100px;
                padding: 0.8rem;
            }
            
            .stat-value {
                font-size: 1.5rem;
            }
            
            .newsletter-form {
                flex-direction: column;
            }
            
            .newsletter-button {
                width: 100%;
            }
        }
        
        @media (max-width: 640px) {
            header {
                flex-direction: column;
                gap: 1.5rem;
                padding: 1rem 5%;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .theme-toggle {
                margin-top: 1rem;
            }
            
            .footer-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-container">
            <div class="loading-logo">Hina<span></span></div>
            <div class="loading-text">Initializing Discord bot features...</div>
            
            <div class="spinner">
                <div class="spinner-dot"></div>
                <div class="spinner-dot"></div>
                <div class="spinner-dot"></div>
                <div class="spinner-dot"></div>
                <div class="spinner-dot"></div>
                <div class="spinner-dot"></div>
                <div class="spinner-dot"></div>
                <div class="spinner-dot"></div>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            
            <div class="loading-stats">
                <div class="stat-item">
                    <div class="stat-value" id="stat-1">0%</div>
                    <div class="stat-label">Loaded</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="stat-2">0ms</div>
                    <div class="stat-label">Response Time</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="stat-3">0</div>
                    <div class="stat-label">Resources</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content (hidden initially) -->
    <div class="main-content" id="mainContent">
        <div class="gradient-bg"></div>
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
        </div>
        <div class="glow-effect glow-1"></div>
        <div class="glow-effect glow-2"></div>
        <div class="particles" id="particles"></div>
        
        <header>
            <div class="logo">Hina<span></span></div>
            <nav>
                <ul>
                    <li><a href="#">Home</a></li>
                    <li><a href="#">Commands</a></li>
                    <li><a href="#">Features</a></li>
                    <li><a href="#">Dashboard</a></li>
                    <li><a href="#">Invite</a></li>
                </ul>
            </nav>
            <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark mode">
                <span class="theme-toggle-icon sun">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </span>
                <span class="theme-toggle-icon moon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                    </svg>
                </span>
                <span class="theme-toggle-thumb"></span>
            </button>
        </header>
        
        <section class="hero">
            <h1>Hina - All-in-One Discord Bot</h1>
            <p>Experience the ultimate Discord bot with music streaming, moderation, utility commands, and more. Clean design meets powerful functionality for your server.</p>
            <button class="cta-button">Invite Hina</button>
        </section>
        
        <section class="features">
            <div class="section-header">
                <h2>Bot Features</h2>
                <p>Discover Hina's comprehensive suite of Discord bot commands and features.</p>
            </div>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                        </svg>
                    </div>
                    <h3>Music Streaming</h3>
                    <p>High-quality music streaming with Spotify integration, autoplay, and queue management. Supports playlists and similar song suggestions.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                    <h3>Moderation Tools</h3>
                    <p>Comprehensive moderation with welcome/goodbye messages, reaction roles, starboard, clownboard, and vanity tracking features.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <h3>Search Commands</h3>
                    <p>YouTube and Google search with clean, minimal design and navigation. Pinterest search and profile management included.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <h3>User Utilities</h3>
                    <p>AFK system with auto-DM, seen command for last messages, and comprehensive user activity tracking across servers.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <h3>Web Dashboard</h3>
                    <p>Clean, minimal dashboard with OAuth2 authentication and permission-based access control for easy server management.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2M7 4h10M7 4l-2 16h14L17 4M9 9v6m6-6v6" />
                        </svg>
                    </div>
                    <h3>Custom Embeds</h3>
                    <p>Beautiful embeds with #323339 color scheme, minimal design, and relative timestamps for all bot responses and features.</p>
                </div>
            </div>
        </section>
        
        <section class="stats">
            <div class="section-header">
                <h2>Hina Statistics</h2>
                <p>Trusted by Discord servers and communities worldwide.</p>
            </div>
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>50+</h3>
                    <p>Commands</p>
                </div>
                <div class="stat-item">
                    <h3>99.9%</h3>
                    <p>Uptime</p>
                </div>
                <div class="stat-item">
                    <h3>1000+</h3>
                    <p>Servers</p>
                </div>
                <div class="stat-item">
                    <h3>24/7</h3>
                    <p>Online</p>
                </div>
            </div>
        </section>
<section class="testimonials">
            <div class="section-header">
                <h2>What Our Clients Say</h2>
                <p>Don't just take our word for it. Here's what our customers have to say.</p>
            </div>
            <div class="testimonial-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "Meowchi has transformed our workflow completely. The clean interface and focus on essentials has dramatically improved our productivity."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">JD</div>
                        <div class="author-info">
                            <h4>John Doe</h4>
                            <p>CEO, DesignCorp</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "The minimalist approach of Meowchi provides clarity we never had before. We've increased our efficiency by 40% since implementation."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">AS</div>
                        <div class="author-info">
                            <h4>Alice Smith</h4>
                            <p>Creative Director</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        "As a designer, I appreciate the aesthetic purity of Meowchi. It's both beautiful and functional - a rare combination."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">RJ</div>
                        <div class="author-info">
                            <h4>Robert Johnson</h4>
                            <p>UX Designer</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="newsletter">
            <div class="newsletter-container">
                <h2>Stay Updated</h2>
                <p>Subscribe to our newsletter for the latest updates, tips, and exclusive offers.</p>
                <form class="newsletter-form">
                    <input type="email" class="newsletter-input" placeholder="Your email address" required>
                    <button type="submit" class="newsletter-button">Subscribe</button>
                </form>
            </div>
        </section>
        
        <footer>
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Meowchi</h3>
                    <p>Minimalist design solutions for the modern digital landscape.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Product</h3>
                    <ul>
                        <li><a href="#">Features</a></li>
                        <li><a href="#">Solutions</a></li>
                        <li><a href="#">Pricing</a></li>
                        <li><a href="#">API</a></li>
                        <li><a href="#">Integrations</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#">About</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#">Press</a></li>
                        <li><a href="#">Partners</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <ul>
                        <li><a href="#">Documentation</a></li>
                        <li><a href="#">Guides</a></li>
                        <li><a href="#">Support</a></li>
                        <li><a href="#">Community</a></li>
                        <li><a href="#">Webinars</a></li>
                    </ul>
                </div>
            </div>
            <div class="copyright">
                &copy; 2023 Meowchi. All rights reserved.
            </div>
        </footer>
        
        <!-- Floating Action Button -->
        <div class="fab">
            <i class="fas fa-comment-dots"></i>
        </div>
    </div>

    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
        
        // Check for saved theme preference or use system preference
        const currentTheme = localStorage.getItem('theme');
        if (currentTheme === 'dark' || (!currentTheme && prefersDarkScheme.matches)) {
            document.body.classList.add('dark-theme');
            themeToggle.classList.add('dark');
        }
        
        // Toggle theme on button click
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');
            themeToggle.classList.toggle('dark');
            
            const theme = document.body.classList.contains('dark-theme') ? 'dark' : 'light';
            localStorage.setItem('theme', theme);
            
            // Update meta theme-color
            const themeColor = document.body.classList.contains('dark-theme') ? '#000000' : '#ffffff';
            document.querySelector('meta[name="theme-color"]').setAttribute('content', themeColor);
        });
        
        // Create particles
        const particlesContainer = document.getElementById('particles');
        const particleCount = 50;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.classList.add('particle');
            
            // Random position
            const posX = Math.random() * 100;
            const posY = Math.random() * 100 + 100; // Start below viewport
            
            // Random size
            const size = Math.random() * 3 + 2;
            
            // Random animation duration
            const duration = Math.random() * 20 + 10;
            
            // Random delay
            const delay = Math.random() * 10;
            
particle.style.left = `${posX}%`;
            particle.style.top = `${posY}%`;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.animationDuration = `${duration}s`;
            particle.style.animationDelay = `-${delay}s`;
            
            particlesContainer.appendChild(particle);
        }
        
        // Simulate loading progress
        const progressBar = document.getElementById('progress-bar');
        const stat1 = document.getElementById('stat-1');
        const stat2 = document.getElementById('stat-2');
        const stat3 = document.getElementById('stat-3');
        const loadingScreen = document.getElementById('loadingScreen');
        const mainContent = document.getElementById('mainContent');
        
        let progress = 0;
        let resourcesLoaded = 0;
        const totalResources = 32;
        
        const loadingInterval = setInterval(() => {
            // Increment progress randomly between 1-5%
            const increment = Math.floor(Math.random() * 5) + 1;
            progress = Math.min(progress + increment, 100);
            
            // Update progress bar
            progressBar.style.width = `${progress}%`;
            
            // Update stats
            stat1.textContent = `${progress}%`;
            
            // Simulate response time improvement
            const responseTime = Math.max(1000 - (progress * 8), 50);
            stat2.textContent = `${responseTime}ms`;
            
            // Simulate resources loading
            if (progress < 100) {
                resourcesLoaded = Math.min(Math.floor(progress / 100 * totalResources), totalResources);
                stat3.textContent = `${resourcesLoaded}/${totalResources}`;
            } else {
                stat3.textContent = `${totalResources}/${totalResources}`;
            }
            
            // When complete, transition to main content
            if (progress >= 100) {
                clearInterval(loadingInterval);
                loadingScreen.style.opacity = '0';
                
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    mainContent.style.opacity = '1';
                    
                    // Add a slight delay before enabling animations to prevent jank
                    setTimeout(() => {
                        document.body.classList.add('loaded');
                    }, 100);
                }, 500);
            }
        }, 200);
        
        // Add some random fluctuation to make it feel more realistic
        setInterval(() => {
            if (progress < 100) {
                const fluctuation = Math.random() > 0.5 ? 1 : -1;
                const currentWidth = parseFloat(progressBar.style.width || '0');
                progressBar.style.width = `${Math.max(0, Math.min(100, currentWidth + fluctuation))}%`;
            }
        }, 300);
        
        // Add meta tag for theme color
        const metaThemeColor = document.createElement('meta');
        metaThemeColor.name = 'theme-color';
        metaThemeColor.content = '#ffffff';
        document.getElementsByTagName('head')[0].appendChild(metaThemeColor);
        
        // Update theme color when theme changes
        const observer = new MutationObserver(() => {
            const themeColor = document.body.classList.contains('dark-theme') ? '#000000' : '#ffffff';
            metaThemeColor.setAttribute('content', themeColor);
        });
        
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });
        
        // Animate elements when they come into view
        const animateOnScroll = () => {
            const elements = document.querySelectorAll('.feature-card, .stat-item, .testimonial-card');
            
            elements.forEach(element => {
                const elementPosition = element.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.3;
                
                if (elementPosition < screenPosition) {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }
            });
        };
        
        // Set initial state for animation
        document.querySelectorAll('.feature-card, .stat-item, .testimonial-card').forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'all 0.6s ease';
        });
        
        // Run on scroll and on load
        window.addEventListener('scroll', animateOnScroll);
        window.addEventListener('load', animateOnScroll);
    </script>
</body>
</html>